.layout-navbar .navbar-dropdown.dropdown-notifications .dropdown-notifications-list .dropdown-notifications-item.marked-as-read .dropdown-notifications-read span {
    background-color: transparent !important;
}

/* Menu Consistency Fixes - Ensure all menu items follow Vuexy template design */
.menu-inner .menu-item .menu-link {
    display: flex;
    align-items: center;
    padding: 0.625rem 1.5rem;
    margin: 0;
    border-radius: 0.375rem;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.menu-inner .menu-item .menu-link .menu-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.menu-inner .menu-item .menu-link div {
    flex: 1;
    font-weight: 400;
    line-height: 1.5;
}

/* Ensure consistent menu header styling */
.menu-inner .menu-header {
    padding: 0.75rem 1.5rem 0.25rem;
    margin-top: 0.75rem;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

.menu-inner .menu-header:first-child {
    margin-top: 0;
}

/* Active menu item styling */
.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) {
    background: linear-gradient(270deg, rgba(115, 103, 240, 0.7) 0%, #7367f0 100%);
    box-shadow: 0px 2px 6px 0px rgba(115, 103, 240, 0.3);
    color: #fff !important;
}

.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) .menu-icon {
    color: #fff !important;
}

.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) div {
    color: #fff !important;
}

/* Hover effects */
.menu-inner .menu-item:not(.active) > .menu-link:hover {
    background: rgba(47, 43, 61, 0.06);
    color: #444050;
}

/* Ensure consistent spacing between menu sections */
.menu-inner .menu-item + .menu-header {
    margin-top: 1rem;
}
