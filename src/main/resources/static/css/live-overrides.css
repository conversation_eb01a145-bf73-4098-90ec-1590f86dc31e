.layout-navbar .navbar-dropdown.dropdown-notifications .dropdown-notifications-list .dropdown-notifications-item.marked-as-read .dropdown-notifications-read span {
    background-color: transparent !important;
}

/* Menu Consistency Fixes - Ensure all menu items follow Vuexy template design */
.menu-inner .menu-item .menu-link {
    display: flex;
    align-items: center;
    padding: 0.625rem 1.5rem;
    margin: 0;
    border-radius: 0.375rem;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.menu-inner .menu-item .menu-link .menu-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.menu-inner .menu-item .menu-link div {
    flex: 1;
    font-weight: 400;
    line-height: 1.5;
}

/* Ensure consistent menu header styling */
.menu-inner .menu-header {
    padding: 0.75rem 1.5rem 0.25rem;
    margin-top: 0.75rem;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

.menu-inner .menu-header:first-child {
    margin-top: 0;
}

/* Active menu item styling */
.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) {
    background: linear-gradient(270deg, rgba(115, 103, 240, 0.7) 0%, #7367f0 100%);
    box-shadow: 0px 2px 6px 0px rgba(115, 103, 240, 0.3);
    color: #fff !important;
}

.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) .menu-icon {
    color: #fff !important;
}

.menu-inner .menu-item.active > .menu-link:not(.menu-toggle) div {
    color: #fff !important;
}

/* Hover effects */
.menu-inner .menu-item:not(.active) > .menu-link:hover {
    background: rgba(47, 43, 61, 0.06);
    color: #444050;
}

/* Ensure consistent spacing between menu sections */
.menu-inner .menu-item + .menu-header {
    margin-top: 1rem;
}

/* Card Consistency Fixes - Ensure consistent card heights and text areas */

/* Ensure all cards in a row have the same height */
.row .card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Make card-body flex to fill available space */
.card .card-body {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Ensure card-text has minimum height for two lines of text */
.card-text {
    min-height: 2.5rem; /* Approximately 2 lines of text at default line-height */
    line-height: 1.25rem; /* Standard line height for readability */
    display: block;
    flex: 1; /* Allow text to expand if needed */
}

/* For cards with buttons, ensure button stays at bottom */
.card-body .btn {
    margin-top: auto;
    align-self: flex-start;
}

/* Specific adjustments for app dashboard cards with flex layout */
.card-body .d-flex .flex-grow-1 .card-text {
    min-height: 2.5rem;
    line-height: 1.25rem;
}

/* Ensure consistent height for admin panel cards */
.card.bg-danger .card-text,
.card.bg-warning .card-text,
.card.bg-info .card-text,
.card.bg-light .card-text {
    min-height: 2.5rem;
    line-height: 1.25rem;
}

/* For cards with white text variants */
.card-text.text-white-50 {
    min-height: 2.5rem;
    line-height: 1.25rem;
}

/* Additional refinements for better visual consistency */

/* Ensure consistent spacing for card titles */
.card-title {
    margin-bottom: 0.75rem !important;
}

/* Improve button positioning in cards */
.card-body .btn-sm {
    margin-top: 0.5rem;
}

/* Handle cards in different row configurations */
.row .col-md-4 .card,
.row .col-md-6 .card {
    height: 100%;
}

/* Ensure proper text wrapping for longer descriptions */
.card-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Specific height adjustments for different card types */
.card.bg-danger .card-body,
.card.bg-warning .card-body,
.card.bg-info .card-body,
.card.bg-primary .card-body,
.card.bg-light .card-body {
    min-height: 140px; /* Ensure minimum card body height */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
