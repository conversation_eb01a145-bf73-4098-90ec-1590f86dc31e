<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<body>

<!-- Breadcrumb Fragment -->
<nav th:fragment="breadcrumb" aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <!-- Home -->
        <li class="breadcrumb-item">
            <a th:href="@{/user/}" class="text-decoration-none">
                <i class="ti ti-home me-1"></i>
                <span th:text="#{menu.dashboard}">Dashboard</span>
            </a>
        </li>
        
        <!-- App Level (if in app context) -->
        <li th:if="${currentAppName != null}" class="breadcrumb-item">
            <a th:href="@{'/app/' + ${currentAppName}}" 
               th:text="#{${'app.name.' + currentAppName}}"
               class="text-decoration-none">
                App Name
            </a>
        </li>
        
        <!-- Current Page (if specified) -->
        <li th:if="${breadcrumbPage != null}" 
            class="breadcrumb-item active" 
            aria-current="page"
            th:text="${breadcrumbPage}">
            Current Page
        </li>
    </ol>
</nav>

<!-- App Header with Breadcrumb -->
<div th:fragment="app-header" th:if="${currentAppName != null}" class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <!--
        &lt;!&ndash; App Title &ndash;&gt;
        <h1 class="h3 mb-1">
            <i th:class="${'me-2 ' + 
                         (currentAppName == 'purchasing' ? 'ti ti-shopping-cart' : 
                         (currentAppName == 'finance' ? 'ti ti-currency-dollar' : 
                         (currentAppName == 'budgets' ? 'ti ti-calculator' : 'ti ti-apps')))}"></i>
            <span th:text="#{${'app.name.' + currentAppName}}">App Name</span>
        </h1>
        
        &lt;!&ndash; App Description &ndash;&gt;
        <p th:if="${appDescription != null}" 
           class="text-muted mb-0" 
           th:text="${appDescription}">
            App description
        </p>
        -->
        <!-- Breadcrumb -->
        <div th:replace="~{fragments/breadcrumb :: breadcrumb}"></div>
    </div>
    
    <!-- User Role Badge -->
    <div th:if="${userRoleLevel != null}">
        <span th:class="${'badge ' + 
                        (userRoleLevel.name() == 'ROLE_ADMIN' ? 'bg-danger' : 
                        (userRoleLevel.name() == 'ROLE_MODERATOR' ? 'bg-warning' : 'bg-primary'))}"
              th:text="${userRoleLevel.name().replace('ROLE_', '')}">
            USER
        </span>
    </div>
</div>

<!-- Quick Navigation for App Pages -->
<div th:fragment="app-navigation" th:if="${currentAppName != null}" class="mb-4">
    <div class="card">
        <div class="card-body py-2">
            <div class="d-flex flex-wrap gap-2">
                <!-- Dashboard Link -->
                <a th:href="@{'/app/' + ${currentAppName}}"
                   th:class="${'btn btn-sm ' + (breadcrumbPage == null or breadcrumbPage == 'Dashboard' ? 'btn-primary' : 'btn-outline-primary')}">
                    <i class="ti ti-dashboard me-1"></i>
                    <span th:text="#{error.403.app.operation.dashboard}">Dashboard</span>
                </a>

                <!-- Settings Link (for moderator+) -->
                <a th:if="${userRoleLevel != null and (userRoleLevel.name() == 'ROLE_MODERATOR' or userRoleLevel.name() == 'ROLE_ADMIN')}"
                   th:href="@{'/app/' + ${currentAppName} + '/settings'}"
                   th:class="${'btn btn-sm ' + (breadcrumbPage == 'Settings' ? 'btn-warning' : 'btn-outline-warning')}">
                    <i class="ti ti-settings me-1"></i>
                    <span th:text="#{error.403.app.operation.settings}">Settings</span>
                </a>

                <!-- Admin Link (for admin only) -->
                <a th:if="${userRoleLevel != null and userRoleLevel.name() == 'ROLE_ADMIN'}"
                   th:href="@{'/app/' + ${currentAppName} + '/admin'}"
                   th:class="${'btn btn-sm ' + (breadcrumbPage == 'Admin' ? 'btn-danger' : 'btn-outline-danger')}">
                    <i class="ti ti-shield me-1"></i>
                    <span th:text="#{error.403.app.operation.admin}">Admin</span>
                </a>
            </div>
        </div>
    </div>
</div>

</body>
</html>
