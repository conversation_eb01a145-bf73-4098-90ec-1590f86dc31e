package ag.fuel.jobify.auth.controller;

import ag.fuel.jobify.auth.dto.LoginUserDto;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.profile.entity.RecentActivity;
import ag.fuel.jobify.profile.service.RecentActivityService;
import ag.fuel.jobify.security.service.AuthenticationService;
import ag.fuel.jobify.security.service.CaptchaService;
import ag.fuel.jobify.security.service.JwtService;
import ag.fuel.jobify.security.service.LoginAttemptService;
import ag.fuel.jobify.auth.dto.LoginResponse;
import ag.fuel.jobify.user.service.UserService;
import io.github.wimdeblauwe.htmx.spring.boot.mvc.HxRequest;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.LocaleResolver;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Controller
@RequiredArgsConstructor
public class JwtController {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final JwtService jwtService;
    private final RecentActivityService recentActivityService;
    private final AuthenticationService authenticationService;
    private final LoginAttemptService loginAttemptService;
    private final CaptchaService captchaService;
    private final MessageSource messageSource;
    private final UserService userService;

    @Value("${google.recaptcha.site-key}")
    private String recaptchaSiteKey;

    @GetMapping("/login")
    public String showLoginPage(Model model, HttpServletRequest request) {
        // Check if user is already authenticated
        if (SecurityContextHolder.getContext().getAuthentication() != null && 
            SecurityContextHolder.getContext().getAuthentication().isAuthenticated() &&
            !(SecurityContextHolder.getContext().getAuthentication() instanceof AnonymousAuthenticationToken)) {
            // User is already logged in, redirect to user page
            return "redirect:/user/";
        }

        // Check if CAPTCHA should be shown (if there have been failed login attempts)
        boolean showCaptcha = loginAttemptService.isBlocked() || captchaService.isEnabled();

        if (showCaptcha) {
            model.addAttribute("showCaptcha", true);
            model.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
        }

        return "login";
    }

    @PostMapping("/token")
    public String generateToken(Model m, @ModelAttribute LoginUserDto loginUserDto, 
                               @RequestParam(name = "g-recaptcha-response", required = false) String recaptchaResponse,
                               @RequestParam(name = "recaptchaRequired", required = false) Boolean recaptchaRequired,
                               HttpServletResponse res, HttpServletRequest request, Locale locale) throws Exception {

        // Debug logging to check if recaptchaResponse is being received
        LOGGER.debug("recaptchaResponse: {}", recaptchaResponse);
        LOGGER.debug("recaptchaRequired: {}", recaptchaRequired);

        // Log all request parameters for debugging
        request.getParameterMap().forEach((key, value) -> {
            LOGGER.debug("Parameter: {} = {}", key, value != null && value.length > 0 ? value[0] : "null");
        });

        // Verify CAPTCHA if required
        if (Boolean.TRUE.equals(recaptchaRequired) && !captchaService.verifyCaptcha(recaptchaResponse)) {
            LOGGER.warn("CAPTCHA verification failed for user: {}", loginUserDto.email());
            m.addAttribute("errorMessage", messageSource.getMessage("page.login.captcha.verify", null, locale));
            m.addAttribute("showCaptcha", true);
            m.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
            return "/login";
        }

        try {
            User authenticatedUser = authenticationService.authenticate(loginUserDto);

            String jwtToken = jwtService.generateToken(authenticatedUser);

            List<String> authorities = authenticatedUser.getAuthorities().stream()
                    .map(item -> item.getAuthority())
                    .collect(Collectors.toList());

            LoginResponse loginResponse = new LoginResponse(jwtToken, jwtService.getExpirationTime(), authorities);

            if (loginResponse != null) {
                // Create a new authentication token with authorities
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                                authenticatedUser,
                                null,
                                authenticatedUser.getAuthorities()
                        );

                // Set the authentication in the SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);
                LOGGER.info("User {} is now authenticated", authenticatedUser.getEmail());

                Cookie cookie = new Cookie(jwtService.getJwtName(), jwtToken);
                cookie.setMaxAge(Math.toIntExact(jwtService.getExpirationTime()));
                cookie.setHttpOnly(true);
                cookie.setSecure(true);
                cookie.setPath("/");
                cookie.setAttribute("SameSite", "Strict");
                res.addCookie(cookie);

                LOGGER.info("user: " + authenticatedUser.getEmail() + " --> token: " + jwtToken);

                /* Save Login Activity */
                try {
                    String userAgentStr = request.getHeader("User-Agent");
                    recentActivityService.addRecentActivity(userAgentStr, authenticatedUser, "Login");
                } catch (Exception e) {
                    LOGGER.error("Unable to Add Recent Activity: " + e.getMessage());
                }
                /* */

                return "redirect:/user/";

            } else {
                m.addAttribute("errorMessage", messageSource.getMessage("auth.error.generic", null, locale));
                m.addAttribute("showCaptcha", true);
                m.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
                return "/login";
            }

        } catch (BadCredentialsException e) {
            m.addAttribute("errorMessage", messageSource.getMessage("page.login.bad.credentials.message", null, locale));
            m.addAttribute("showCaptcha", true);
            m.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
            return "/login";
        } catch (DisabledException e) {
            m.addAttribute("errorMessage", messageSource.getMessage("auth.error.user.disabled", null, locale));
            m.addAttribute("showCaptcha", true);
            m.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
            return "/login";
        } catch (LockedException e) {
            m.addAttribute("errorMessage", e.getMessage());
            m.addAttribute("showCaptcha", true);
            m.addAttribute("recaptchaSiteKey", recaptchaSiteKey);
            return "/login";
        }
    }

    @GetMapping("/log_out")
    public String logout(HttpServletRequest request, HttpServletResponse res, Model m, Locale locale) {

        String msg = null;

        // Set isAuthenticated to false
        if (SecurityContextHolder.getContext().getAuthentication() != null) {
            // Get current user before clearing authentication
            User currentUser = userService.getCurrentUser();

            SecurityContextHolder.getContext().getAuthentication().setAuthenticated(false);

            // Log recent activity if user was found
            if (currentUser != null) {
                String userAgentStr = request.getHeader("User-Agent");
                recentActivityService.addRecentActivity(userAgentStr, currentUser, "Logout");
            }
            LOGGER.info("User is now logged out");
        }

        Cookie[] cookies2 = request.getCookies();
        if (cookies2 != null) {
            for (int i = 0; i < cookies2.length; i++) {
                if (cookies2[i].getName().equals(jwtService.getJwtName())) {
                    cookies2[i].setMaxAge(0);
                    res.addCookie(cookies2[i]);
                    msg = messageSource.getMessage("auth.logout.success", null, locale);
                }
            }
        }

        m.addAttribute("msg", msg);

        return "/login";
    }

    /**
     * HTMX-compatible logout endpoint for automatic logout when JWT expires
     */
    @GetMapping("/htmx-logout")
    @HxRequest
    public ResponseEntity<String> htmxLogout(HttpServletRequest request, HttpServletResponse response) {
        LOGGER.info("HTMX logout triggered - JWT token expired");

        // Clear authentication context
        if (SecurityContextHolder.getContext().getAuthentication() != null) {
            SecurityContextHolder.getContext().getAuthentication().setAuthenticated(false);
            LOGGER.info("User authentication cleared due to token expiration");
        }

        // Clear JWT cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(jwtService.getJwtName())) {
                    cookie.setMaxAge(0);
                    cookie.setPath("/");
                    response.addCookie(cookie);
                    LOGGER.info("JWT cookie cleared");
                }
            }
        }

        // Set HTMX redirect header to login page
        response.setHeader("HX-Redirect", "/login?expired=true");

        return ResponseEntity.ok("Logged out due to token expiration");
    }
}
