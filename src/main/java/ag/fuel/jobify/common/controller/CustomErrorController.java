package ag.fuel.jobify.common.controller;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.LocaleResolver;

import java.sql.SQLException;
import java.util.Locale;

@Controller
@RequiredArgsConstructor
public class CustomErrorController implements ErrorController {

    private static final Logger logger = LoggerFactory.getLogger(CustomErrorController.class);
    private final MessageSource messageSource;
    private final LocaleResolver localeResolver;

    @RequestMapping("/error")
    public String handleError(HttpServletRequest request, Model model) {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        Object exception = request.getAttribute(RequestDispatcher.ERROR_EXCEPTION);
        Object requestUri = request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);

        // Check if this is a browser/tool noise request
        boolean isNoiseRequest = isNoiseRequest(requestUri != null ? requestUri.toString() : null);

        if (isNoiseRequest) {
            logger.debug("Browser/tool noise request - Status: {}, URI: {}, Exception: {}",
                        status, requestUri, exception != null ? exception.getClass().getSimpleName() : "null");
        } else {
            logger.info("Error handler called - Status: {}, URI: {}, Exception: {}",
                       status, requestUri, exception != null ? exception.getClass().getSimpleName() : "null");
        }

        // Check for database connection errors
        if (exception != null) {
            Throwable throwable = (Throwable) exception;
            logger.error("Error exception: {}", throwable.getClass().getName());

            // Check if this is a database connection error
            if (isDatabaseConnectionError(throwable)) {
                logger.error("Database connection error detected: {}", throwable.getMessage());
                return "/errors/database-connection-error";
            }
        }

        if (status != null) {
            Integer statusCode = Integer.valueOf(status.toString());

            if (isNoiseRequest) {
                logger.debug("Processing status code: {}", statusCode);
            } else {
                logger.info("Processing status code: {}", statusCode);
            }

            if (statusCode == HttpStatus.NOT_FOUND.value()) {
                if (isNoiseRequest) {
                    logger.debug("Returning 404 error page for noise request");
                } else {
                    logger.info("Returning 404 error page");
                }
                return "/errors/error-404";
            } else if (statusCode == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
                logger.info("Returning 500 error page");
                return "/errors/error-500";
            } else if (statusCode == HttpStatus.FORBIDDEN.value()) {
                logger.info("Returning 403 error page");
                return handle403Error(request, model);
            }
        }

        if (isNoiseRequest) {
            logger.debug("Returning generic error page for noise request");
        } else {
            logger.info("Returning generic error page");
        }
        return "/errors/generic-error";
    }

    /**
     * Handle 403 Forbidden errors with detailed information for app routes.
     */
    private String handle403Error(HttpServletRequest request, Model model) {
        Locale locale = localeResolver.resolveLocale(request);
        String requestUri = (String) request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);

        // Check if this is an app route
        if (requestUri != null && requestUri.startsWith("/app/")) {
            String[] pathParts = requestUri.split("/");
            if (pathParts.length >= 3) {
                String appName = pathParts[2];
                model.addAttribute("requestedApp", appName);

                // Check if it's a specific operation
                if (pathParts.length >= 4) {
                    String operation = pathParts[3];
                    String operationKey = "operation." + operation.toLowerCase();
                    String localizedOperation = messageSource.getMessage(operationKey, null, capitalizeFirstLetter(operation), locale);
                    model.addAttribute("requestedOperation", localizedOperation);
                }
            }

            // Return our custom 403 page for app routes
            return "error/403";
        }

        // For non-app routes, return the standard error page
        return "/errors/error-403";
    }

    /**
     * Utility method to capitalize first letter of a string.
     */
    private String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /**
     * Checks if the given throwable is a database connection error.
     * 
     * @param throwable The throwable to check
     * @return true if the throwable is a database connection error, false otherwise
     */
    private boolean isDatabaseConnectionError(Throwable throwable) {
        // Check if the throwable is a database connection error
        if (throwable instanceof SQLException || 
            throwable instanceof DataAccessResourceFailureException || 
            throwable instanceof CannotGetJdbcConnectionException) {
            return true;
        }

        // Check if the cause is a database connection error
        if (throwable.getCause() != null) {
            return isDatabaseConnectionError(throwable.getCause());
        }

        // Check if the message contains connection closed indicators
        String message = throwable.getMessage();
        if (message != null && (
            message.contains("connection is closed") || 
            message.contains("Connection refused") || 
            message.contains("No connection") ||
            message.contains("Connection timed out"))) {
            return true;
        }

        return false;
    }

    /**
     * Checks if the given URI represents a browser/tool noise request that should be logged at DEBUG level.
     * These are common requests made by browsers, development tools, and crawlers that typically result in 404s.
     *
     * @param requestUri The request URI to check
     * @return true if the request is considered noise, false otherwise
     */
    private boolean isNoiseRequest(String requestUri) {
        if (requestUri == null) {
            return false;
        }

        // Common browser/tool requests that generate noise in logs
        return requestUri.equals("/favicon.ico") ||
               requestUri.equals("/robots.txt") ||
               requestUri.equals("/sitemap.xml") ||
               requestUri.startsWith("/apple-touch-icon") ||
               requestUri.startsWith("/.well-known/") ||
               requestUri.contains("apple-touch-icon-precomposed") ||
               requestUri.contains("apple-touch-icon") ||
               requestUri.equals("/browserconfig.xml") ||
               requestUri.equals("/manifest.json") ||
               requestUri.equals("/sw.js") ||
               requestUri.equals("/service-worker.js");
    }
}
