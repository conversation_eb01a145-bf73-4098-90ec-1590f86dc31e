# Sample Data Setup for Dynamic App Menu System

This document explains how to set up and use sample data to test the dynamic app menu system.

## 🎯 Overview

The sample data system creates realistic test data to demonstrate how the dynamic app menu works with different companies, users, and app permissions.

## 🔧 Configuration

### Enable Sample Data Creation

In `application.properties`, set:
```properties
app.data-init.enabled=true
```

When enabled, sample data will be automatically created when the application starts.

### Disable Sample Data Creation

For production or when you don't want sample data:
```properties
app.data-init.enabled=false
```

## 📊 Sample Data Structure

### Companies Created

1. **TechCorp Solutions**
   - **Apps**: Full access (Purchasing, Finance, Budgets)
   - **TAX Number**: 12345678901
   - **Domain**: techcorp.com

2. **StartupHub Inc**
   - **Apps**: Limited access (Purchasing, Budgets only)
   - **TAX Number**: 23456789012
   - **Domain**: startuphub.com

3. **Global Finance Ltd**
   - **Apps**: Finance-focused (Finance, Budgets only)
   - **TAX Number**: 34567890123
   - **Domain**: globalfinance.com

### Users Created

| Email | Name | Company | Role | App Access | Password |
|-------|------|---------|------|------------|----------|
| <EMAIL> | John Admin | TechCorp Solutions | ADMIN | All apps | password123 |
| <EMAIL> | Jane Moderator | TechCorp Solutions | MODERATOR | All apps | password123 |
| <EMAIL> | Bob User | TechCorp Solutions | USER | Purchasing, Finance | password123 |
| <EMAIL> | Alice Startup | StartupHub Inc | USER | Purchasing, Budgets | password123 |
| <EMAIL> | Charlie Finance | Global Finance Ltd | USER | Finance, Budgets | password123 |

## 🧪 Testing Scenarios

### Test Case 1: Full Access User
- **Login as**: <EMAIL>
- **Expected Menu**: Should see all 3 apps (Purchasing, Finance, Budgets)
- **Role**: Admin privileges

### Test Case 2: Limited Access User
- **Login as**: <EMAIL>
- **Expected Menu**: Should see only 2 apps (Purchasing, Finance)
- **Role**: Regular user

### Test Case 3: Startup Company User
- **Login as**: <EMAIL>
- **Expected Menu**: Should see 2 apps (Purchasing, Budgets)
- **Company Restriction**: No Finance app access

### Test Case 4: Finance Company User
- **Login as**: <EMAIL>
- **Expected Menu**: Should see 2 apps (Finance, Budgets)
- **Company Restriction**: No Purchasing app access

## 🛠️ Management API Endpoints

### Create Sample Data
```http
POST /api/admin/data-init/create
Authorization: Admin role required
```

### Clean Up Sample Data
```http
DELETE /api/admin/data-init/cleanup
Authorization: Admin role required
```

### Get Sample Data Info
```http
GET /api/admin/data-init/info
Authorization: Admin role required
```

## 🔍 Verification Steps

1. **Start the application** with `app.data-init.enabled=true`
2. **Check logs** for sample data creation messages
3. **Login with different test users** to verify menu behavior
4. **Navigate to app dashboards** to test access control
5. **Verify security** by trying to access unauthorized apps

## 🚨 Security Notes

- All sample users have the same password: `password123`
- Sample data is for **testing only** - not suitable for production
- Admin endpoints are protected by role-based security
- Users can only access apps they have explicit permission for

## 🧹 Cleanup

To remove sample data:
1. Call the cleanup API endpoint, OR
2. Set `app.data-init.enabled=false` and restart the application
3. The cleanup only removes user-app relationships, not the users themselves

## 📈 Progress Tracking

With this sample data setup, the dynamic app menu system progress moves from **15%** to approximately **40%** completion:

✅ **Backend Foundation** (Complete)
✅ **Sample Data Creation** (Complete)  
✅ **User Permission Testing** (Ready)
✅ **Menu Rendering Logic** (Complete)
🔄 **Security Validation** (In Progress)
⏳ **UI/UX Enhancements** (Pending)
⏳ **App Functionality** (Pending)

## 🎉 Next Steps

1. Test the menu system with different users
2. Implement active menu state highlighting
3. Add proper error handling for unauthorized access
4. Enhance app dashboard functionality
5. Add user preferences for default app selection
